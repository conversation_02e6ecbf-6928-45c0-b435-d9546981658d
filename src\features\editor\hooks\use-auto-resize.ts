import { fabric } from "fabric";
import { useCallback, useEffect } from "react";

interface UseAutoResizeProps {
  canvas: fabric.Canvas | null;
  container: HTMLDivElement | null;
}

export const useAutoResize = ({ canvas, container }: UseAutoResizeProps) => {
  const autoZoom = useCallback(() => {
    if (!canvas || !container) return;

    const containerWidth = container.offsetWidth;
    const containerHeight = container.offsetHeight;

    // Ensure we have valid dimensions
    if (containerWidth <= 0 || containerHeight <= 0) {
      console.warn("Container has invalid dimensions for autoZoom", { containerWidth, containerHeight });
      return;
    }

    // Don't resize canvas - keep it at workspace dimensions
    // The canvas should match the workspace size, not container size

    const center = canvas.getCenter();

    const zoomRatio = 0.85;
    const localWorkspace = canvas
      .getObjects()
      .find((object) => object.name === "clip");

    if (!localWorkspace) return;

    // Validate workspace dimensions
    const workspaceWidth = localWorkspace.width || 0;
    const workspaceHeight = localWorkspace.height || 0;

    if (workspaceWidth <= 0 || workspaceHeight <= 0) {
      console.warn("Workspace has invalid dimensions for autoZoom", { workspaceWidth, workspaceHeight });
      return;
    }

    // Calculate scale to fit workspace in container
    const padding = 40; // 20px padding on each side
    const availableWidth = containerWidth - padding;
    const availableHeight = containerHeight - padding;

    const scaleX = availableWidth / workspaceWidth;
    const scaleY = availableHeight / workspaceHeight;
    const scale = Math.min(scaleX, scaleY, 1); // Don't scale up beyond 100%

    const zoom = scale * zoomRatio;

    canvas.setViewportTransform(fabric.iMatrix.concat());
    canvas.zoomToPoint(new fabric.Point(center.left, center.top), zoom);

    const workspaceCenter = localWorkspace.getCenterPoint();
    const viewportTransform = canvas.viewportTransform;

    if (
      canvas.width === undefined ||
      canvas.height === undefined ||
      !viewportTransform
    ) {
      return;
    }

    viewportTransform[4] = canvas.width / 2 - workspaceCenter.x * viewportTransform[0];

    viewportTransform[5] = canvas.height / 2 - workspaceCenter.y * viewportTransform[3];

    canvas.setViewportTransform(viewportTransform);

    localWorkspace.clone((cloned: fabric.Rect) => {
      canvas.clipPath = cloned;
      canvas.requestRenderAll();
    });
  }, [canvas, container]);

  // Note: ResizeObserver is handled in the main component to avoid conflicts

  return { autoZoom };
};
