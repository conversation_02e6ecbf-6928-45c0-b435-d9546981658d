import { fabric } from "fabric";
import { useCallback, useEffect } from "react";

interface UseAutoResizeProps {
  canvas: fabric.Canvas | null;
  container: HTMLDivElement | null;
}

export const useAutoResize = ({ canvas, container }: UseAutoResizeProps) => {
  const autoZoom = useCallback(() => {
    if (!canvas || !container) return;

    const containerWidth = container.offsetWidth - 40; // Account for padding
    const containerHeight = container.offsetHeight - 40; // Account for padding

    // Ensure we have valid dimensions
    if (containerWidth <= 0 || containerHeight <= 0) {
      console.warn("Container has invalid dimensions for autoZoom", { containerWidth, containerHeight });
      return;
    }

    const localWorkspace = canvas
      .getObjects()
      .find((object) => object.name === "clip");

    if (!localWorkspace) return;

    // Validate workspace dimensions
    const workspaceWidth = localWorkspace.width || 0;
    const workspaceHeight = localWorkspace.height || 0;

    if (workspaceWidth <= 0 || workspaceHeight <= 0) {
      console.warn("Workspace has invalid dimensions for autoZoom", { workspaceWidth, workspaceHeight });
      return;
    }

    // Calculate scale to fit workspace in container with some padding
    const scaleX = containerWidth / workspaceWidth;
    const scaleY = containerHeight / workspaceHeight;
    const scale = Math.min(scaleX, scaleY) * 0.85; // 85% to leave some margin

    // Reset viewport transform first
    canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

    // Get workspace center
    const workspaceCenter = localWorkspace.getCenterPoint();

    // Calculate canvas center
    const canvasCenter = canvas.getCenter();

    // Apply zoom at workspace center
    canvas.zoomToPoint(new fabric.Point(workspaceCenter.x, workspaceCenter.y), scale);

    // Center the workspace in the canvas
    const viewportTransform = canvas.viewportTransform;
    if (viewportTransform) {
      viewportTransform[4] = canvasCenter.left - workspaceCenter.x * scale;
      viewportTransform[5] = canvasCenter.top - workspaceCenter.y * scale;
      canvas.setViewportTransform(viewportTransform);
    }

    // Set clip path
    localWorkspace.clone((cloned: fabric.Rect) => {
      canvas.clipPath = cloned;
      canvas.requestRenderAll();
    });
  }, [canvas, container]);

  // Disable ResizeObserver to prevent infinite loops
  // Auto-zoom will be called manually when needed
  useEffect(() => {
    // Initial auto-zoom when canvas and container are ready
    if (canvas && container) {
      setTimeout(() => {
        autoZoom();
      }, 100);
    }
  }, [canvas, container, autoZoom]);

  return { autoZoom };
};
