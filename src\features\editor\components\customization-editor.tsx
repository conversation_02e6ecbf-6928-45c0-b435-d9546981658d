"use client";

import { useEffect, useRef, useCallback, useState } from "react";
import { fabric } from "fabric";
import { useEditor } from "@/features/editor/hooks/use-editor";
import { Footer } from "@/features/editor/components/footer";
import { Toolbar } from "@/features/editor/components/toolbar";
import { EditableLayer } from "@/types/template";
import debounce from "lodash.debounce";
import { Loader2 } from "lucide-react";

interface CustomizationEditorProps {
  templateData: {
    id: string;
    name: string;
    width: number;
    height: number;
    json: string;
    editableLayers: EditableLayer[];
  };
  customizations: Record<string, string>;
  onCustomizationChange: (layerId: string, value: string) => void;
  onPreviewGenerated: (dataUrl: string) => void;
  activeLayerId?: string | null;
  onLayerActivation?: (layerId: string | null) => void;
}

export const CustomizationEditor = ({
  templateData,
  customizations,
  onCustomizationChange,
  onPreviewGenerated,
  activeLayerId: externalActiveLayerId,
  onLayerActivation,
}: CustomizationEditorProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isCanvasLoading, setIsCanvasLoading] = useState(true);

  // Debug: Log when component mounts
  useEffect(() => {
    console.log('CustomizationEditor mounted with templateData:', {
      width: templateData.width,
      height: templateData.height,
      editableLayers: templateData.editableLayers.length,
      hasJson: !!templateData.json,
      jsonLength: templateData.json?.length || 0
    });

    // Try to parse and log JSON structure
    if (templateData.json) {
      try {
        const parsedJson = JSON.parse(templateData.json);
        console.log('Template JSON structure:', {
          version: parsedJson.version,
          objectCount: parsedJson.objects?.length || 0,
          objects: parsedJson.objects?.map((obj: any) => ({
            type: obj.type,
            id: obj.id,
            name: obj.name
          })) || []
        });
      } catch (error) {
        console.error('Error parsing template JSON:', error);
      }
    }
  }, []);

  // Initialize editor first
  const { init, editor } = useEditor({
    defaultState: templateData.json,
    defaultWidth: templateData.width,
    defaultHeight: templateData.height,
    clearSelectionCallback: () => {
      onLayerActivation?.(null);
    },
    saveCallback: () => {
      // Generate preview when canvas changes
      generatePreview();
    },
  });

  // Debug: Log editor state
  useEffect(() => {
    if (editor) {
      console.log('Editor initialized:', {
        hasCanvas: !!editor.canvas,
        hasAutoZoom: !!editor.autoZoom,
        canvasObjects: editor.canvas?.getObjects().length || 0
      });
    }
  }, [editor]);

  // Generate preview from canvas (defined after editor)
  const generatePreview = useCallback(() => {
    if (!editor?.canvas) return;

    try {
      const workspace = editor.canvas.getObjects().find((obj: any) => obj.name === "clip");
      if (!workspace) return;

      const dataUrl = editor.canvas.toDataURL({
        format: 'png',
        quality: 0.9,
        multiplier: 0.5,
      });
      onPreviewGenerated(dataUrl);
    } catch (error) {
      console.error('Failed to generate preview:', error);
    }
  }, [editor, onPreviewGenerated]);

  // Initialize canvas (same as main editor)
  useEffect(() => {
    let retryCount = 0;
    const maxRetries = 10;

    const initializeCanvas = () => {
      if (!containerRef.current || !canvasRef.current) {
        console.log('Container or canvas ref not available');
        return false;
      }

      const container = containerRef.current;
      const containerWidth = container.offsetWidth;
      const containerHeight = container.offsetHeight;

      console.log(`Canvas init attempt ${retryCount + 1}/${maxRetries}:`, { containerWidth, containerHeight });

      if (containerWidth === 0 || containerHeight === 0) {
        console.log('Container has zero dimensions, retrying...');
        return false;
      }

      // Ensure minimum dimensions
      const finalWidth = Math.max(containerWidth, 300);
      const finalHeight = Math.max(containerHeight, 200);

      const canvas = new fabric.Canvas(canvasRef.current, {
        controlsAboveOverlay: true,
        preserveObjectStacking: true,
        width: finalWidth,
        height: finalHeight,
      });

      console.log('Canvas initialized with dimensions:', canvas.getWidth(), 'x', canvas.getHeight());

      init({
        initialCanvas: canvas,
        initialContainer: container,
      });

      return canvas;
    };

    const attemptInit = () => {
      const canvas = initializeCanvas();

      if (!canvas && retryCount < maxRetries) {
        retryCount++;
        const delay = Math.min(100 * retryCount, 1000); // Progressive delay up to 1 second
        console.log(`Canvas init failed, retrying in ${delay}ms...`);
        setTimeout(attemptInit, delay);
      } else if (!canvas) {
        console.error('Failed to initialize canvas after maximum retries');
        setIsCanvasLoading(false);
      }

      return canvas;
    };

    // Start initialization
    const canvas = attemptInit();

    return () => {
      if (canvas) {
        canvas.dispose();
      }
    };
  }, [init]);

  // Add ResizeObserver to handle container size changes
  useEffect(() => {
    if (!containerRef.current || !editor?.canvas) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        if (width > 0 && height > 0 && editor?.canvas) {
          console.log('Resizing canvas to:', width, 'x', height);
          editor.canvas.setDimensions({ width, height });

          // Debounced autoZoom to prevent too frequent calls
          setTimeout(() => {
            if (editor?.autoZoom) {
              editor.autoZoom();
            }
          }, 100);
        }
      }
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [editor]);

  // Auto-zoom when editor is ready and has content
  useEffect(() => {
    if (!editor?.canvas || !editor.autoZoom) return;

    console.log('Editor is ready, triggering auto-zoom');

    // Wait for canvas to be fully loaded with content
    const checkAndAutoZoom = () => {
      const objects = editor.canvas.getObjects();
      console.log(`Canvas has ${objects.length} objects, checking for content...`);

      if (objects.length > 1) { // More than just the workspace
        console.log('Canvas has content, triggering auto-zoom');
        editor.autoZoom();
        setIsCanvasLoading(false);
      } else {
        // If no objects yet, wait a bit more (max 5 seconds)
        const maxRetries = 25; // 25 * 200ms = 5 seconds
        const currentRetry = (checkAndAutoZoom as any).retryCount || 0;

        if (currentRetry < maxRetries) {
          (checkAndAutoZoom as any).retryCount = currentRetry + 1;
          setTimeout(checkAndAutoZoom, 200);
        } else {
          console.warn('Template loading timeout - no content found after 5 seconds');
          setIsCanvasLoading(false);
        }
      }
    };

    // Small delay to ensure canvas is ready
    setTimeout(checkAndAutoZoom, 300);
  }, [editor]);

  // Handle selection changes to notify parent
  useEffect(() => {
    if (!editor?.canvas) return;

    const handleSelectionCreated = (e: fabric.IEvent) => {
      const target = e.target;
      if (target) {
        const layerId = (target as any).id;
        const layer = templateData.editableLayers.find(l => l.id === layerId);
        if (layer) {
          onLayerActivation?.(layerId);
        }
      }
    };

    const handleSelectionCleared = () => {
      onLayerActivation?.(null);
    };

    const handleTextChanged = (e: fabric.IEvent) => {
      const target = e.target;
      if (target && target.type === 'textbox') {
        const layerId = (target as any).id;
        const layer = templateData.editableLayers.find(l => l.id === layerId);
        if (layer && layer.type === 'text') {
          const currentText = (target as fabric.Textbox).text || '';
          onCustomizationChange(layerId, currentText);
        }
      }
    };

    editor.canvas.on('selection:created', handleSelectionCreated);
    editor.canvas.on('selection:updated', handleSelectionCreated);
    editor.canvas.on('selection:cleared', handleSelectionCleared);
    editor.canvas.on('text:changed', handleTextChanged);

    return () => {
      editor.canvas.off('selection:created', handleSelectionCreated);
      editor.canvas.off('selection:updated', handleSelectionCreated);
      editor.canvas.off('selection:cleared', handleSelectionCleared);
      editor.canvas.off('text:changed', handleTextChanged);
    };
  }, [editor, templateData.editableLayers, onLayerActivation, onCustomizationChange]);

  // Debounced function to apply customizations
  const applyCustomizations = useCallback(
    debounce(() => {
      if (!editor?.canvas) return;

      console.log('Applying customizations:', customizations);

      templateData.editableLayers.forEach(layer => {
      const customValue = customizations[layer.id];
      console.log(`Processing layer ${layer.id} (${layer.type}):`, customValue);

      const canvasObject = editor.canvas.getObjects().find((obj: any) => obj.id === layer.id);
      if (!canvasObject) {
        console.log(`Canvas object not found for layer ${layer.id}`);
        return;
      }

      if (layer.type === 'text' && canvasObject.type === 'textbox') {
        const textbox = canvasObject as fabric.Textbox;
        console.log(`Current text: "${textbox.text}", New text: "${customValue}"`);

        if (customValue && textbox.text !== customValue) {
          console.log(`Updating text for ${layer.id} to: "${customValue}"`);
          textbox.set('text', customValue);
          if (editor?.canvas) {
            editor.canvas.renderAll();
          }

          // Trigger save to update the canvas state
          if (editor?.canvas?.fire) {
            editor.canvas.fire('text:changed', { target: textbox });
          }
        }
      } else if (layer.type === 'image' && customValue) {
        console.log(`Replacing image for ${layer.id} with: ${customValue}`);

        // Check if this image is already being processed to prevent infinite loops
        const currentSrc = (canvasObject as fabric.Image).getSrc?.();
        if (currentSrc === customValue) {
          console.log(`Image ${layer.id} already has the correct source, skipping replacement`);
          return;
        }

        // Handle image replacement
        fabric.Image.fromURL(customValue, (img) => {
          if (!editor.canvas) {
            console.error('Canvas not available during image replacement');
            return;
          }

          if (!img) {
            console.error('Failed to load image from URL:', customValue);
            return;
          }

          console.log('Original image object:', canvasObject);
          console.log('New image loaded:', img);

          // Get ALL the original object's properties to preserve exact positioning
          const originalProps = {
            left: canvasObject.left,
            top: canvasObject.top,
            width: canvasObject.width,
            height: canvasObject.height,
            scaleX: canvasObject.scaleX,
            scaleY: canvasObject.scaleY,
            angle: canvasObject.angle,
            originX: canvasObject.originX,
            originY: canvasObject.originY,
            flipX: canvasObject.flipX,
            flipY: canvasObject.flipY,
            opacity: canvasObject.opacity,
            visible: canvasObject.visible,
            shadow: canvasObject.shadow,
            stroke: canvasObject.stroke,
            strokeWidth: canvasObject.strokeWidth,
            fill: canvasObject.fill,
            selectable: canvasObject.selectable,
            evented: canvasObject.evented,
            hasControls: canvasObject.hasControls,
            hasBorders: canvasObject.hasBorders,
            lockMovementX: canvasObject.lockMovementX,
            lockMovementY: canvasObject.lockMovementY,
            lockRotation: canvasObject.lockRotation,
            lockScalingX: canvasObject.lockScalingX,
            lockScalingY: canvasObject.lockScalingY,
            lockUniScaling: canvasObject.lockUniScaling,
          };

          console.log('Preserving original properties:', originalProps);

          // Calculate scale to fit within original bounds while maintaining aspect ratio
          const targetWidth = (originalProps.width || 100) * (originalProps.scaleX || 1);
          const targetHeight = (originalProps.height || 100) * (originalProps.scaleY || 1);

          const scaleX = targetWidth / img.width!;
          const scaleY = targetHeight / img.height!;
          const scale = Math.min(scaleX, scaleY);

          // Apply all original properties to the new image, but keep the new scaling
          img.set({
            ...originalProps,
            scaleX: scale,
            scaleY: scale,
          });

          // Ensure the image is positioned exactly like the original
          img.setCoords();

          // Set the ID using a custom property
          (img as any).id = layer.id;

          // Get the exact position in the layer stack
          const objectIndex = editor.canvas.getObjects().indexOf(canvasObject);
          console.log(`Replacing image at index ${objectIndex}`);

          // Only proceed if we found the object
          if (objectIndex === -1) {
            console.warn(`Cannot find object ${layer.id} in canvas objects`);
            return;
          }

          // Remove old image and insert new one at exact same position
          editor.canvas.remove(canvasObject);
          editor.canvas.insertAt(img, objectIndex, false);

          // Ensure the image is properly initialized before rendering
          try {
            // Update coordinates first
            img.setCoords();

            // Render without controls first
            if (editor?.canvas) {
              editor.canvas.renderAll();
            }

            // Small delay before selecting to avoid the getRetinaScaling error
            setTimeout(() => {
              try {
                // Select the new image to show it's been replaced
                if (editor?.canvas && editor.canvas.getContext && img.canvas === editor.canvas) {
                  editor.canvas.setActiveObject(img);
                  editor.canvas.renderAll();
                }

                console.log(`Image replaced for ${layer.id} at index ${objectIndex}`, img);
              } catch (selectionError) {
                console.warn('Error selecting new image:', selectionError);
                // Still render the canvas even if selection fails
                if (editor?.canvas) {
                  editor.canvas.renderAll();
                }
              }
            }, 100);
          } catch (renderError) {
            console.error('Error during image replacement:', renderError);
            // Fallback: just render the canvas
            if (editor?.canvas) {
              editor.canvas.renderAll();
            }
          }
        }, {
          crossOrigin: 'anonymous'
        });
      }
    });
    }, 300), // 300ms debounce
    [editor, templateData.editableLayers, customizations]
  );

  // Apply customizations from sidebar to canvas
  useEffect(() => {
    applyCustomizations();
  }, [customizations, applyCustomizations]);

  // Ensure canvas objects have proper IDs when editor loads
  useEffect(() => {
    if (!editor?.canvas) return;

    const canvas = editor.canvas;
    const editableLayerIds = templateData.editableLayers.map(layer => layer.id);

    console.log('Setting up canvas object IDs for editable layers:', editableLayerIds);

    // Wait a bit for the canvas to be fully loaded
    setTimeout(() => {
      const allObjects = canvas.getObjects();
      console.log(`Canvas loaded with ${allObjects.length} objects`);

      // Log only editable objects for debugging
      const editableObjects = allObjects.filter((obj: any) =>
        editableLayerIds.includes(obj.id) ||
        obj.type === 'textbox' ||
        obj.type === 'image'
      );
      console.log('Editable/relevant objects:', editableObjects.map((obj: any) => ({
        id: obj.id,
        type: obj.type,
        name: obj.name,
        text: obj.type === 'textbox' ? obj.text : undefined
      })));

      // Try to match objects to editable layers by content or position
      templateData.editableLayers.forEach((layer) => {
        let matchedObject = allObjects.find((obj: any) => obj.id === layer.id);

        if (!matchedObject) {
          if (layer.type === 'text') {
            // Try to find by text content if no ID match
            matchedObject = allObjects.find((obj: any) =>
              obj.type === 'textbox' &&
              obj.text === layer.originalValue &&
              !editableLayerIds.includes(obj.id)
            );
          } else if (layer.type === 'image') {
            // For images, try to find by type and position, or just by type if it's the nth image
            const imageObjects = allObjects.filter((obj: any) =>
              obj.type === 'image' && !editableLayerIds.includes(obj.id)
            );

            // Try to match by index (first editable image layer matches first unmatched image object)
            const imageLayerIndex = templateData.editableLayers
              .filter(l => l.type === 'image')
              .indexOf(layer);

            if (imageObjects[imageLayerIndex]) {
              matchedObject = imageObjects[imageLayerIndex];
            }
          }
        }

        if (matchedObject && !(matchedObject as any).id) {
          console.log(`Assigning ID ${layer.id} to ${layer.type} object:`, matchedObject);
          (matchedObject as any).id = layer.id;
        }
      });

      try {
        canvas.renderAll();

        // Trigger autoZoom after objects are loaded and IDs are assigned
        setTimeout(() => {
          if (editor?.autoZoom) {
            console.log('Auto-zooming after template load and ID assignment');
            editor.autoZoom();
            // Ensure loading state is cleared
            setIsCanvasLoading(false);
          }
        }, 300);
      } catch (error) {
        console.error('Error rendering canvas after ID assignment:', error);
      }
    }, 500);
  }, [editor, templateData.editableLayers]);

  // Function to select object by layer ID (called from parent)
  useEffect(() => {
    if (!editor?.canvas) return;

    console.log('External active layer changed:', externalActiveLayerId);

    if (externalActiveLayerId) {
      const allObjects = editor.canvas.getObjects();
      console.log('All canvas objects:', allObjects.map((obj: any) => ({ id: obj.id, type: obj.type, name: obj.name })));

      const targetObject = allObjects.find((obj: any) => obj.id === externalActiveLayerId);
      console.log('Found target object:', targetObject);

      if (targetObject) {
        console.log('Selecting object in canvas:', (targetObject as any).id);
        editor.canvas.setActiveObject(targetObject);
        editor.canvas.renderAll();
      } else {
        console.log('Target object not found for ID:', externalActiveLayerId);
      }
    } else {
      // Clear selection if no layer is active
      console.log('Clearing canvas selection');
      editor.canvas.discardActiveObject();
      editor.canvas.renderAll();
    }
  }, [editor, externalActiveLayerId]);

  return (
    <div className="customization-editor-container w-full h-full flex flex-col bg-muted">
      <Toolbar
        editor={editor}
        activeTool="select"
        onChangeActiveTool={() => {}}
        key={JSON.stringify(editor?.canvas.getActiveObject())}
      />
      <div
        className="customization-canvas-container flex-1 bg-muted overflow-hidden relative w-full"
        ref={containerRef}
        style={{ minHeight: '400px' }}
      >
        {isCanvasLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-muted/80 z-10">
            <div className="flex flex-col items-center space-y-3 bg-white p-6 rounded-lg shadow-lg">
              <Loader2 className="h-10 w-10 animate-spin text-blue-600" />
              <div className="text-center">
                <p className="text-lg font-medium text-gray-900">Loading Template</p>
                <p className="text-sm text-gray-600">Please wait while we prepare your editor...</p>
              </div>
            </div>
          </div>
        )}
        <canvas
          ref={canvasRef}
          className="block w-full h-full"
          style={{
            display: 'block',
            margin: '0 auto',
            maxWidth: '100%',
            maxHeight: '100%',
          }}
        />
      </div>
      <Footer editor={editor} />
    </div>
  );
};
