"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { Loader2, ArrowLeft, Download, Upload, Type, Image as ImageIcon } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

import { TemplateResponse, EditableLayer } from "@/types/template";
import { CustomizationEditor } from "@/features/editor/components/customization-editor";

export default function CustomizeTemplatePage() {
  const params = useParams();
  const router = useRouter();
  const [template, setTemplate] = useState<TemplateResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [customizations, setCustomizations] = useState<Record<string, string>>({});
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [activeLayerId, setActiveLayerId] = useState<string | null>(null);

  useEffect(() => {
    const fetchTemplate = async () => {
      try {
        const response = await fetch(`/api/projects/public/${params.projectId}`);

        if (!response.ok) {
          if (response.status === 404) {
            setError("Template not found or not public");
          } else {
            setError("Failed to load template");
          }
          return;
        }

        const data = await response.json();
        const templateData = data.data;

        // Check if template is customizable
        if (!templateData.isCustomizable || !templateData.editableLayers) {
          setError("This template is not customizable");
          return;
        }

        // Parse editable layers safely
        let editableLayers: EditableLayer[] = [];
        try {
          editableLayers = templateData.editableLayers ? JSON.parse(templateData.editableLayers) : [];
        } catch (error) {
          console.error('Error parsing editable layers:', error);
          editableLayers = [];
        }

        console.log('Template loaded:', {
          templateData,
          editableLayers,
          json: templateData.json ? JSON.parse(templateData.json) : null
        });

        setTemplate({
          ...templateData,
          editableLayers,
        });

        // Initialize customizations with original values
        const initialCustomizations: Record<string, string> = {};
        editableLayers.forEach(layer => {
          initialCustomizations[layer.id] = layer.originalValue || '';
        });
        setCustomizations(initialCustomizations);

      } catch (err) {
        setError("Failed to load template");
      } finally {
        setLoading(false);
      }
    };

    if (params.projectId) {
      fetchTemplate();
    }
  }, [params.projectId]);

  const handleCustomizationChange = (layerId: string, value: string) => {
    console.log('Customization change:', layerId, value);
    setCustomizations(prev => ({
      ...prev,
      [layerId]: value,
    }));
  };

  const handleImageUpload = async (layerId: string, file: File) => {
    console.log('Image upload started for layer:', layerId, file);

    // Validate file type
    if (!file.type.startsWith('image/')) {
      console.error('Invalid file type:', file.type);
      return;
    }

    try {
      // Create object URL for immediate preview
      const imageUrl = URL.createObjectURL(file);
      console.log('Created object URL:', imageUrl);

      // Update customizations to trigger canvas update
      handleCustomizationChange(layerId, imageUrl);

      // Auto-select the layer
      handleLayerActivation(layerId);
    } catch (error) {
      console.error('Error handling image upload:', error);
    }
  };

  const handlePreviewGenerated = (dataUrl: string) => {
    setPreviewUrl(dataUrl);
  };

  const handleLayerActivation = (layerId: string | null) => {
    setActiveLayerId(layerId);
  };

  // Initialize preview with template thumbnail
  useEffect(() => {
    if (template?.thumbnailUrl) {
      setPreviewUrl(template.thumbnailUrl);
    }
  }, [template]);

  const generatePreview = async () => {
    setIsGeneratingPreview(true);
    try {
      // Small delay to show loading state
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (err) {
      console.error('Failed to generate preview:', err);
    } finally {
      setIsGeneratingPreview(false);
    }
  };

  const downloadCustomized = async () => {
    setIsDownloading(true);
    try {
      // This would call an API to generate and download the customized design
      // For now, we'll simulate it and download the current preview
      await new Promise(resolve => setTimeout(resolve, 2000));

      if (previewUrl) {
        const link = document.createElement('a');
        link.href = previewUrl;
        link.download = `customized-${template?.name || 'design'}.png`;
        link.click();
      }
    } catch (err) {
      console.error('Failed to download:', err);
    } finally {
      setIsDownloading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
      </div>
    );
  }

  if (error || !template) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {error || "Template not found"}
            </h1>
            <Button onClick={() => router.push("/public")} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Gallery
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                onClick={() => router.push(`/public/${params.projectId}`)} 
                variant="ghost" 
                size="sm"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div className="h-6 w-px bg-gray-300" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Customize: {template.name}
                </h1>
                <p className="text-sm text-gray-500">
                  Make this template your own
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={generatePreview}
                disabled={isGeneratingPreview}
                variant="outline"
              >
                {isGeneratingPreview ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : null}
                Preview
              </Button>
              <Button
                onClick={downloadCustomized}
                disabled={isDownloading}
                className="bg-purple-600 hover:bg-purple-700"
              >
                {isDownloading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                Download
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-200px)]">
          {/* Customization Panel */}
          <div className="lg:col-span-1 lg:max-h-full overflow-y-auto">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Customization Tools</CardTitle>
                <p className="text-sm text-gray-500">
                  Click on elements in the editor to select and customize them
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                {template.editableLayers.map((layer) => (
                  <div
                    key={layer.id}
                    className={`space-y-3 p-3 rounded-lg border-2 transition-all cursor-pointer ${
                      activeLayerId === layer.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => {
                      console.log('Clicking layer:', layer.id, 'Current active:', activeLayerId);
                      if (activeLayerId === layer.id) {
                        handleLayerActivation(null); // Deselect if already active
                      } else {
                        handleLayerActivation(layer.id); // Select layer
                      }
                    }}
                  >
                    <div className="flex items-center space-x-2">
                      <Badge variant={
                        activeLayerId === layer.id
                          ? 'default'
                          : layer.type === 'text' ? 'outline' : 'secondary'
                      }>
                        {layer.type === 'text' ? (
                          <Type className="h-3 w-3 mr-1" />
                        ) : (
                          <ImageIcon className="h-3 w-3 mr-1" />
                        )}
                        {layer.type}
                      </Badge>
                      <span className={`font-medium text-sm ${
                        activeLayerId === layer.id ? 'text-blue-900' : 'text-gray-700'
                      }`}>{layer.name}</span>
                      {activeLayerId === layer.id && (
                        <div className="ml-auto">
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                        </div>
                      )}
                    </div>

                    {layer.type === 'text' ? (
                      <div>
                        <Label className="text-sm text-gray-600">
                          {layer.placeholder || 'Enter text'}
                        </Label>
                        <Input
                          value={customizations[layer.id] || layer.originalValue || ''}
                          onChange={(e) => handleCustomizationChange(layer.id, e.target.value)}
                          placeholder={layer.placeholder}
                          maxLength={layer.constraints?.maxLength}
                          className={`mt-1 ${
                            activeLayerId === layer.id
                              ? 'border-blue-500 focus:border-blue-600'
                              : ''
                          }`}
                        />
                        {activeLayerId === layer.id && (
                          <p className="text-xs text-blue-600 mt-1">
                            ✓ Selected - Type here or double-click in editor
                          </p>
                        )}
                        {layer.constraints?.maxLength && (
                          <p className="text-xs text-gray-500 mt-1">
                            {customizations[layer.id]?.length || 0}/{layer.constraints.maxLength} characters
                          </p>
                        )}
                      </div>
                    ) : (
                      <div>
                        <Label className="text-sm text-gray-600">
                          Upload your image
                        </Label>
                        <div className="mt-1 space-y-3">
                          <input
                            type="file"
                            accept={layer.constraints?.allowedFormats?.map(f => `.${f}`).join(',') || 'image/*'}
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) {
                                handleImageUpload(layer.id, file);
                                handleLayerActivation(layer.id); // Auto-select when uploading
                              }
                            }}
                            className={`block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold ${
                              activeLayerId === layer.id
                                ? 'file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'
                                : 'file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100'
                            }`}
                          />
                          {activeLayerId === layer.id && (
                            <p className="text-xs text-blue-600">
                              ✓ Selected - Upload an image to replace
                            </p>
                          )}

                          {/* Small preview of uploaded image */}
                          {customizations[layer.id] && (
                            <div className="flex items-center space-x-2">
                              <img
                                src={customizations[layer.id]}
                                alt="Uploaded preview"
                                className="w-12 h-12 object-cover rounded border"
                              />
                              <div className="flex-1">
                                <p className="text-xs text-gray-600">Image uploaded</p>
                                <button
                                  onClick={() => {
                                    setCustomizations(prev => {
                                      const updated = { ...prev };
                                      delete updated[layer.id];
                                      return updated;
                                    });
                                  }}
                                  className="text-xs text-red-500 hover:text-red-700"
                                >
                                  Remove
                                </button>
                              </div>
                            </div>
                          )}

                          {layer.constraints?.maxFileSize && (
                            <p className="text-xs text-gray-500">
                              Max size: {Math.round(layer.constraints.maxFileSize / 1024 / 1024)}MB
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Editor Panel */}
          <div className="lg:col-span-3 h-full">
            <Card className="h-full w-full">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Template Editor</CardTitle>
                <p className="text-sm text-gray-500">
                  Click to select elements • Double-click text to edit • Use toolbar for formatting
                </p>
              </CardHeader>
              <CardContent className="h-[calc(100%-90px)] p-2">
                <CustomizationEditor
                  templateData={{
                    id: template.id,
                    name: template.name,
                    width: template.width,
                    height: template.height,
                    json: template.json,
                    editableLayers: template.editableLayers,
                  }}
                  customizations={customizations}
                  onCustomizationChange={handleCustomizationChange}
                  onPreviewGenerated={handlePreviewGenerated}
                  activeLayerId={activeLayerId}
                  onLayerActivation={handleLayerActivation}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
